from typing import TypeVar
from uuid import UUID, uuid4

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.reporting.type.query_config import (
    ExpressionColumnConfig,
    FieldColumnConfig,
    QueryConfig,
)
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.core.base import TableModel
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetField,
    ReportingDatasetFieldDataType,
    ReportingDatasetSource,
    ReportingDatasetType,
)
from salestech_be.util.time import zoned_utc_now

T = TypeVar("T", bound=TableModel)


class ReportingDatasetRepository(GenericRepository):
    def _extract_dataset_ids_from_query_config(
        self, query_config: QueryConfig
    ) -> set[UUID]:
        """Extract dataset IDs from QueryConfig.

        Args:
            query_config: The query configuration to extract dataset IDs from

        Returns:
            Set of dataset UUIDs found in the query config
        """
        dataset_ids: set[UUID] = set()

        # Extract dataset IDs from primary_dataset
        dataset_ids.add(query_config.primary_dataset.dataset_id)

        # Extract dataset IDs from additional_datasets
        for join_config in query_config.additional_datasets:
            dataset_ids.add(join_config.dataset.dataset_id)

        return dataset_ids

    async def _extract_dataset_fields_from_query_config(
        self,
        *,
        query_config: QueryConfig,
        organization_id: UUID,
    ) -> dict[UUID, ReportingDatasetField]:
        dataset_ids = self._extract_dataset_ids_from_query_config(query_config)
        return await self.map_dataset_fields_by_ids(
            dataset_ids=dataset_ids,
            organization_id=organization_id,
        )

    async def _extract_columns_from_query_config(
        self,
        *,
        query_config: QueryConfig,
        organization_id: UUID,
    ) -> list[tuple[str, str | None, ReportingDatasetFieldDataType]]:
        """
        Extract column information from QueryConfig.

        Returns:
            List of tuples containing (name, description, data_type)
        """
        columns = []

        map_dataset_fields = await self._extract_dataset_fields_from_query_config(
            query_config=query_config,
            organization_id=organization_id,
        )
        for column_config in query_config.columns:
            if isinstance(column_config, FieldColumnConfig):
                # For field columns, use the field name and alias
                field_id = column_config.dataset_field.field.field_id
                dataset_field: ReportingDatasetField = map_dataset_fields[field_id]
                name = column_config.alias or dataset_field.name
                description = name
                data_type = dataset_field.data_type
            elif isinstance(column_config, ExpressionColumnConfig):
                # For expression columns, use the alias as name
                name = column_config.alias or column_config.to_sql()
                description = name
                # TODO resolve data_type based on expression
                data_type = ReportingDatasetFieldDataType.TEXT
            else:
                raise InvalidArgumentError(
                    f"Unsupported column config type: {column_config}"
                )

            columns.append((name, description, data_type))
        return columns

    async def create_dataset(
        self,
        *,
        name: str,
        description: str | None,
        query_config: QueryConfig,
        dataset_source: ReportingDatasetSource,
        user_id: UUID,
        organization_id: UUID,
    ) -> tuple[ReportingDataset, list[ReportingDatasetField]]:
        """Create a new dataset with fields extracted from query_config."""
        async with self.engine.begin():
            # Create the dataset
            dataset = ReportingDataset(
                id=uuid4(),
                name=name,
                description=description,
                source=dataset_source,
                type=ReportingDatasetType.QUERY,
                query_config=query_config,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
            )
            created_dataset = await self.insert(dataset)

            # Extract columns from query_config and create dataset fields
            column_info = await self._extract_columns_from_query_config(
                query_config=query_config,
                organization_id=organization_id,
            )
            created_fields = []

            for column_name, column_description, data_type in column_info:
                field = ReportingDatasetField(
                    id=uuid4(),
                    dataset_id=created_dataset.id,
                    name=column_name,
                    description=column_description,
                    data_type=data_type,
                    organization_id=organization_id,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_id,
                )
                created_field = await self.insert(field)
                created_fields.append(created_field)

            return created_dataset, created_fields

    async def patch_dataset(
        self,
        *,
        dataset_id: UUID,
        name: UnsetAware[str] = UNSET,
        description: UnsetAware[str | None] = UNSET,
        query_config: UnsetAware[QueryConfig] = UNSET,
        user_id: UUID,
        organization_id: UUID,
    ) -> tuple[ReportingDataset, list[ReportingDatasetField]]:
        """Patch a dataset following the standard patch pattern."""
        # Get existing dataset
        db_dataset = await self.find_by_tenanted_primary_key_or_fail(
            ReportingDataset,
            id=dataset_id,
            organization_id=organization_id,
        )

        # Only allow updating QUERY type datasets
        if db_dataset.type != ReportingDatasetType.QUERY:
            raise InvalidArgumentError("Only QUERY type datasets can be updated")

        # Update a dataset and update its fields if query_config is provided
        async with self.engine.begin():
            # Build update data
            update_data = {}
            if specified(name):
                update_data["name"] = name
            if specified(description):
                update_data["description"] = description
            if specified(query_config):
                update_data["query_config"] = query_config

            update_data["updated_at"] = zoned_utc_now()
            update_data["updated_by_user_id"] = user_id

            # Update dataset
            updated_dataset = await self.update_by_tenanted_primary_key(
                ReportingDataset,
                primary_key_to_value={"id": dataset_id},
                column_to_update=update_data,
                organization_id=organization_id,
            )

            # Get existing fields
            dataset_fields = await self._find_by_column_values(
                ReportingDatasetField,
                dataset_id=dataset_id,
                organization_id=organization_id,
            )

            # If query_config is provided, update the fields
            if specified(query_config):
                updated_dataset_fields = []

                # Extract columns from query_config
                column_info = await self._extract_columns_from_query_config(
                    query_config=query_config,
                    organization_id=organization_id,
                )

                for column_name, column_description, data_type in column_info:
                    # Check if the field already exists
                    existing_field = next(
                        (
                            field
                            for field in dataset_fields
                            if field.name == column_name
                            and field.data_type == data_type
                        ),
                        None,
                    )

                    if existing_field:
                        # Check if the description needs to be updated
                        if existing_field.description != column_description:
                            updated_dataset_fields.append(existing_field)
                            continue

                        # Update the existing field
                        updated_field = await self.update_by_tenanted_primary_key(
                            ReportingDatasetField,
                            primary_key_to_value={"id": existing_field.id},
                            column_to_update={
                                "description": column_description,
                            },
                        )
                        updated_dataset_fields.append(updated_field)

                    else:
                        # Create a new field
                        field = ReportingDatasetField(
                            id=uuid4(),
                            dataset_id=dataset_id,
                            name=column_name,
                            description=column_description,
                            data_type=data_type,
                            organization_id=organization_id,
                            created_at=zoned_utc_now(),
                            created_by_user_id=user_id,
                        )
                        created_field = await self.insert(field)
                        updated_dataset_fields.append(created_field)

                # Delete existing fields that are not in the updated list
                deleted_dataset_ids = [
                    field.id
                    for field in dataset_fields
                    if field not in updated_dataset_fields
                ]
                if deleted_dataset_ids:
                    await self._update_by_column_values(
                        ReportingDatasetField,
                        column_value_to_query={
                            "dataset_id": deleted_dataset_ids,
                            "organization_id": organization_id,
                        },
                        column_to_update={
                            "deleted_at": zoned_utc_now(),
                            "deleted_by_user_id": user_id,
                        },
                    )

            else:
                updated_dataset_fields = dataset_fields

            return updated_dataset, updated_dataset_fields

    async def list_datasets(
        self,
        *,
        organization_id: UUID,
    ) -> list[ReportingDataset]:
        return await self._find_by_column_values(
            ReportingDataset,
            organization_id=[organization_id, None],
        )

    async def list_dataset_fields(
        self,
        *,
        dataset_id: UUID,
        organization_id: UUID,
    ) -> list[ReportingDatasetField]:
        return await self._find_by_column_values(
            ReportingDatasetField,
            dataset_id=dataset_id,
            organization_id=[organization_id, None],
        )

    async def map_dataset_fields_by_ids(
        self,
        *,
        dataset_ids: set[UUID],
        organization_id: UUID,
    ) -> dict[UUID, ReportingDatasetField]:
        dataset_fields = await self._find_by_column_values(
            ReportingDatasetField,
            dataset_id=list(dataset_ids),
            organization_id=[organization_id, None],
        )
        return {dataset_field.id: dataset_field for dataset_field in dataset_fields}

    async def get_dataset(
        self,
        *,
        dataset_id: UUID,
        organization_id: UUID,
    ) -> ReportingDataset:
        return await self._find_unique_by_column_values_or_fail(
            ReportingDataset,
            id=dataset_id,
            organization_id=[organization_id, None],
        )

    async def delete_dataset(
        self,
        *,
        dataset_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        async with self.engine.begin():
            # Create update data with soft delete fields
            update_data = {
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            }

            # Delete dataset field
            await self._update_by_column_values(
                ReportingDatasetField,
                column_value_to_query={
                    "dataset_id": dataset_id,
                    "organization_id": organization_id,
                },
                column_to_update=update_data,
            )

            # Delete dataset
            await self.update_by_tenanted_primary_key(
                ReportingDataset,
                primary_key_to_value={"id": dataset_id},
                column_to_update=update_data,
                organization_id=organization_id,
            )
