import asyncio
from decimal import Decimal
from typing import get_args, get_origin
from uuid import UUID, uuid4

from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.account import Account, AccountStatus
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.core.types import CreatedSource, EntityParticipant
from salestech_be.db.models.pipeline import Pipeline, PipelineStatus
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetField,
    ReportingDatasetFieldDataType,
    ReportingDatasetSource,
    ReportingDatasetType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.str import PhoneNumber
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


async def get_db_engine() -> DatabaseEngine:
    """Get database engine with proper configuration."""
    engine = DatabaseEngine(
        str(settings.db_url),
        echo=settings.db_echo,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
    )
    if settings.db_conn_prewarm:
        await engine.prewarm_db_connection()
    return engine


def map_python_type_to_reporting_data_type(python_type: type) -> ReportingDatasetFieldDataType:
    """Map Python types to ReportingDatasetFieldDataType."""
    # Handle Union types (e.g., str | None)
    origin = get_origin(python_type)
    if origin is not None:
        # Handle Union types like str | None
        if hasattr(python_type, "__args__"):
            args = get_args(python_type)
            # Filter out None type and get the actual type
            non_none_args = [arg for arg in args if arg is not type(None)]
            if non_none_args:
                python_type = non_none_args[0]
        # Handle list types
        elif origin is list:
            return ReportingDatasetFieldDataType.JSON

    # Direct type mappings
    if python_type is UUID:
        return ReportingDatasetFieldDataType.UUID
    elif python_type is str:
        return ReportingDatasetFieldDataType.TEXT
    elif python_type is EmailStrLower:
        return ReportingDatasetFieldDataType.TEXT
    elif python_type is PhoneNumber:
        return ReportingDatasetFieldDataType.TEXT
    elif python_type is ZoneRequiredDateTime:
        return ReportingDatasetFieldDataType.DATETIME
    elif python_type is int:
        return ReportingDatasetFieldDataType.INTEGER
    elif python_type is Decimal:
        return ReportingDatasetFieldDataType.FLOAT
    elif python_type is bool:
        return ReportingDatasetFieldDataType.BOOLEAN
    elif python_type is AccountStatus:
        return ReportingDatasetFieldDataType.TEXT
    elif python_type is PipelineStatus:
        return ReportingDatasetFieldDataType.TEXT
    elif python_type is CreatedSource:
        return ReportingDatasetFieldDataType.TEXT
    elif python_type is EntityParticipant:
        return ReportingDatasetFieldDataType.JSON
    elif hasattr(python_type, "__origin__") and python_type.__origin__ is list:
        return ReportingDatasetFieldDataType.JSON
    else:
        # Handle additional types by checking their name
        type_name = getattr(python_type, "__name__", str(python_type))
        if "EmailStr" in type_name:
            return ReportingDatasetFieldDataType.TEXT
        elif "Decimal" in type_name:
            return ReportingDatasetFieldDataType.FLOAT
        elif "datetime" in type_name.lower() or "DateTime" in type_name:
            return ReportingDatasetFieldDataType.DATETIME
        elif "int" in type_name.lower() or "Integer" in type_name:
            return ReportingDatasetFieldDataType.INTEGER
        elif "bool" in type_name.lower() or "Boolean" in type_name:
            return ReportingDatasetFieldDataType.BOOLEAN
        else:
            # Default to STRING for unknown types
            logger.warning(f"Unknown type {python_type}, defaulting to STRING")
            return ReportingDatasetFieldDataType.TEXT


def extract_model_fields(model_class: type) -> list[tuple[str, ReportingDatasetFieldDataType]]:
    """Extract field names and types from a Pydantic model class."""
    fields = []

    # Get all annotations from the class
    annotations = getattr(model_class, "__annotations__", {})

    for field_name, field_type in annotations.items():
        # Skip private fields and class variables
        if field_name.startswith("_") or field_name in ["table_name", "ordered_primary_keys"]:
            continue

        # Handle Annotated types (Column[Type] and JsonColumn[Type])
        # Use the model's column_fields which contains the parsed metadata
        if hasattr(model_class, "column_fields") and field_name in model_class.column_fields:
            column_info = model_class.column_fields[field_name]
            if column_info.mapped_column.is_json:
                data_type = ReportingDatasetFieldDataType.JSON
            else:
                # Extract the inner type from the field annotation
                args = get_args(field_type)
                if args:
                    inner_type = args[0]
                    data_type = map_python_type_to_reporting_data_type(inner_type)
                else:
                    # Fallback: try to get the type from the field_info
                    field_info = column_info.field_info
                    if hasattr(field_info, 'annotation') and field_info.annotation:
                        # Extract from annotation
                        annotation_args = get_args(field_info.annotation)
                        if annotation_args:
                            inner_type = annotation_args[0]
                            data_type = map_python_type_to_reporting_data_type(inner_type)
                        else:
                            data_type = ReportingDatasetFieldDataType.TEXT
                    else:
                        data_type = ReportingDatasetFieldDataType.TEXT

            fields.append((field_name, data_type))
            continue

        # Handle regular type annotations
        data_type = map_python_type_to_reporting_data_type(field_type)
        fields.append((field_name, data_type))

    return fields


async def create_table_dataset(
    repository: ReportingRepository,
    table_name: str,
    model_class: type,
    description: str | None = None,
) -> tuple[ReportingDataset, list[ReportingDatasetField]]:
    """Create a table dataset and its fields for a given model class."""
    now = zoned_utc_now()

    # Create the dataset
    dataset = ReportingDataset(
        id=uuid4(),
        name=table_name,
        description=description,
        source=ReportingDatasetSource.DATABASE,
        type=ReportingDatasetType.TABLE,
        table_reference=table_name,
        query_config=None,
        sql_statement=None,
        organization_id=None,  # Table datasets are not org-specific
        created_at=now,
        created_by_user_id=None,
        updated_at=None,
        updated_by_user_id=None,
        deleted_at=None,
        deleted_by_user_id=None,
    )

    # Insert the dataset
    inserted_dataset = await repository.create_dataset(dataset)
    logger.info(f"Inserted dataset: {table_name} with ID: {inserted_dataset.id}")

    # Extract fields from the model class
    model_fields = extract_model_fields(model_class)

    # Create dataset fields
    created_fields = []
    for field_name, data_type in model_fields:
        field = ReportingDatasetField(
            id=uuid4(),
            dataset_id=inserted_dataset.id,
            name=field_name,
            description=None,
            data_type=data_type,
            organization_id=None,
            created_at=now,
            created_by_user_id=None,
            updated_at=None,
            updated_by_user_id=None,
            deleted_at=None,
            deleted_by_user_id=None,
        )

        created_field = await repository.create_dataset_field(field)
        created_fields.append(created_field)
        logger.debug(f"Created field: {field_name} ({data_type}) for dataset {table_name}")

    logger.info(f"Created {len(created_fields)} fields for dataset {table_name}")
    return inserted_dataset, created_fields


async def insert_table_datasets() -> None:
    """Insert table datasets for contact, account, and pipeline tables."""
    db_engine = await get_db_engine()
    reporting_repository = ReportingRepository(engine=db_engine)

    # Define the tables to create datasets for
    tables_to_process = [
        ("contact", Contact, "Contact table dataset"),
        ("account", Account, "Account table dataset"),
        ("pipeline", Pipeline, "Pipeline table dataset"),
    ]

    total_datasets = 0
    total_fields = 0

    for table_name, model_class, description in tables_to_process:
        try:
            _, fields = await create_table_dataset(
                repository=reporting_repository,
                table_name=table_name,
                model_class=model_class,
                description=description,
            )
            total_datasets += 1
            total_fields += len(fields)

        except Exception as e:
            logger.error(f"Error creating dataset for table {table_name}: {e!s}")
            raise

    logger.info(f"Successfully inserted {total_datasets} table datasets with {total_fields} total fields")


async def main() -> None:
    """Main function to run the backfill script."""
    logger.info("Starting table dataset insertion")
    await insert_table_datasets()
    logger.info("Table dataset insertion completed")


if __name__ == "__main__":
    asyncio.run(main())