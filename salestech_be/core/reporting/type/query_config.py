from __future__ import annotations

import enum
from datetime import date, datetime
from typing import Annotated, Any, Literal
from uuid import UUID

from pydantic import BaseModel, Field, ValidationInfo, field_validator

from salestech_be.core.reporting.type.filter import Filter
from salestech_be.util.enum_util import NameValueStrEnum


def escape_sql_string(value: str) -> str:
    """Escape a string value for SQL by doubling single quotes"""
    return value.replace("'", "''")


def format_sql_value(  # type: ignore[explicit-any]
    value: Any,
) -> str:
    """Format a value appropriately for SQL based on its type"""
    if value is None:
        return "NULL"
    elif isinstance(value, str):
        return f"'{escape_sql_string(value)}'"
    elif isinstance(value, bool):
        return "TRUE" if value else "FALSE"
    elif isinstance(value, (int, float)):
        return str(value)
    elif isinstance(value, (UUID, datetime, date)):
        return f"'{value!s}'"
    else:
        # For any other type, convert to string and quote it
        return f"'{escape_sql_string(str(value))}'"


class DatasetConfig(BaseModel):
    """Configuration for a dataset"""

    dataset_id: UUID
    dataset_name: str
    alias: str | None = None

    def to_sql(self) -> str:
        """Convert the dataset to SQL"""
        return (
            f"{self.dataset_name} AS {self.alias}" if self.alias else self.dataset_name
        )

    def to_alias_sql(self) -> str:
        """Convert the dataset to SQL"""
        return f"{self.alias}" if self.alias else self.dataset_name


class FieldConfig(BaseModel):
    """Configuration for a field"""

    field_id: UUID
    field_name: str

    def to_sql(self) -> str:
        """Convert the field to SQL"""
        return f"{self.field_name}"


class DatasetFieldConfig(BaseModel):
    """Configuration for a field in the dataset"""

    dataset: DatasetConfig
    field: FieldConfig

    def to_sql(self) -> str:
        """Convert the field reference to SQL"""
        return f"{self.dataset.to_alias_sql()}.{self.field.to_sql()}"


class ExpressionNodeType(NameValueStrEnum):
    """Types of nodes in an expression tree"""

    FUNCTION = "FUNCTION"
    FIELD = "FIELD"
    LITERAL = "LITERAL"
    OPERATOR = "OPERATOR"
    CASE = "CASE"


class BaseExpressionNode(BaseModel):
    """Base class for all expression tree nodes"""

    type: str

    def to_sql(self) -> str:
        """Convert the expression node to SQL"""
        raise NotImplementedError("Subclasses must implement to_sql")


class LiteralExpressionNode(BaseExpressionNode):  # type: ignore[explicit-any]
    """A literal value in an expression"""

    type: Literal["LITERAL"] = "LITERAL"
    value: Any  # type: ignore[explicit-any]
    value_type: str | None = None  # e.g., "string", "number", "date", etc.

    def to_sql(self) -> str:
        """Convert the literal to SQL"""
        return format_sql_value(self.value)


class FieldExpressionNode(BaseExpressionNode):
    """A reference to a field in a dataset"""

    type: Literal["FIELD"] = "FIELD"
    dataset_field: DatasetFieldConfig

    def to_sql(self) -> str:
        """Convert the field reference to SQL"""
        return self.dataset_field.to_sql()


class FunctionExpressionNode(BaseExpressionNode):
    """A function call in an expression"""

    type: Literal["FUNCTION"] = "FUNCTION"
    function_name: str
    arguments: list[ExpressionNode]

    def to_sql(self) -> str:
        """Convert the function to SQL"""
        args_sql = ", ".join(arg.to_sql() for arg in self.arguments)
        return f"{self.function_name}({args_sql})"


class OperatorExpressionNode(BaseExpressionNode):
    """An operator in an expression (e.g., +, -, *, /)"""

    type: Literal["OPERATOR"] = "OPERATOR"
    operator: str
    left: ExpressionNode
    right: ExpressionNode

    def to_sql(self) -> str:
        """Convert the operator expression to SQL"""
        return f"({self.left.to_sql()} {self.operator} {self.right.to_sql()})"


class CaseWhenCondition(BaseModel):
    """A WHEN condition in a CASE expression"""

    condition: ExpressionNode
    result: ExpressionNode


class CaseExpressionNode(BaseExpressionNode):
    """A CASE expression"""

    type: Literal["CASE"] = "CASE"
    conditions: list[CaseWhenCondition]
    else_result: ExpressionNode | None = None

    def to_sql(self) -> str:
        """Convert the CASE expression to SQL"""
        when_clauses = " ".join(
            f"WHEN {cond.condition.to_sql()} THEN {cond.result.to_sql()}"
            for cond in self.conditions
        )
        else_clause = f" ELSE {self.else_result.to_sql()}" if self.else_result else ""
        return f"CASE {when_clauses}{else_clause} END"


ExpressionNode = Annotated[
    LiteralExpressionNode
    | FieldExpressionNode
    | FunctionExpressionNode
    | OperatorExpressionNode
    | CaseExpressionNode,
    Field(discriminator="type"),
]


class FieldColumnConfig(BaseModel):
    """Configuration for a field in the dataset"""

    type: Literal["FIELD"]
    dataset_field: DatasetFieldConfig
    alias: str | None = None

    def to_sql(self) -> str:
        """Convert the field to SQL"""
        if self.alias:
            return f"{self.dataset_field.to_sql()} AS {self.alias}"
        return self.dataset_field.to_sql()


class ExpressionColumnConfig(BaseModel):
    """Configuration for an expression column in the dataset"""

    type: Literal["EXPRESSION"]
    expression: str | ExpressionNode  # Support both string and tree structure
    alias: str | None = None

    def to_sql(self) -> str:
        """Convert the expression to SQL"""
        expression_sql = (
            self.expression
            if isinstance(self.expression, str)
            else self.expression.to_sql()
        )
        if self.alias:
            return f"{expression_sql} AS {self.alias}"
        return expression_sql


ColumnConfig = Annotated[
    FieldColumnConfig | ExpressionColumnConfig, Field(discriminator="type")
]


class JoinOperator(enum.StrEnum):
    EQUALS = "="

    def to_sql(self) -> str:
        """Convert the join operator to SQL"""
        return self.value


class JoinType(NameValueStrEnum):
    LEFT = "LEFT"
    INNER = "INNER"

    def to_sql(self) -> str:
        """Convert the join type to SQL"""
        return self.value.upper()


class JoinCondition(BaseModel):
    """Defines how two tables should be joined"""

    left_dataset_field: DatasetFieldConfig
    operator: JoinOperator
    right_dataset_field: DatasetFieldConfig


class JoinConfig(BaseModel):
    """Configuration for a table join"""

    dataset: DatasetConfig
    join_type: JoinType
    condition: JoinCondition


class FilterOperator(enum.StrEnum):
    EQUALS = "="
    NOT_EQUALS = "!="
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_THAN_EQUALS = ">="
    LESS_THAN_EQUALS = "<="
    LIKE = "LIKE"
    IN = "IN"
    NOT_IN = "NOT IN"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"

    def to_sql(self) -> str:
        """Convert the filter operator to SQL"""
        return self.value


class FilterConfig(BaseModel):  # type: ignore[explicit-any]
    """Configuration for a WHERE clause filter condition"""

    dataset_field: DatasetFieldConfig
    operator: FilterOperator
    value: Any = None  # type: ignore[explicit-any]

    @field_validator("value")
    @classmethod
    def validate_value(  # type: ignore[explicit-any]
        cls,
        v: Any,
        info: ValidationInfo,
    ) -> Any:
        values = info.data
        if "operator" in values:
            operator = values["operator"]
            # For IS NULL and IS NOT NULL, value should be None
            if (
                operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]
                and v is not None
            ):
                raise ValueError(f"Value must be None for {operator.to_sql()} operator")
            # For all other operators, value should not be None
            elif (
                operator not in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]
                and v is None
            ):
                raise ValueError(
                    f"Value cannot be None for {operator.to_sql()} operator"
                )
            # For IN and NOT IN, value should be a list
            if operator in [
                FilterOperator.IN,
                FilterOperator.NOT_IN,
            ] and not isinstance(v, list):
                raise ValueError(
                    f"Value must be a list for {operator.to_sql()} operator"
                )
        return v

    @classmethod
    def from_filter(cls, filter_obj: Filter, dataset: DatasetConfig) -> FilterConfig:
        """
        Convert a Filter object to a FilterConfig object.

        Args:
            filter_obj: The Filter object to convert
            dataset_name: Optional dataset name to use. If not provided, uses the column name.

        Returns:
            FilterConfig: The converted filter configuration

        Raises:
            ValueError: If the filter operator is not supported or invalid
        """
        # Map Filter operators to FilterOperator enum
        operator_mapping = {
            "=": FilterOperator.EQUALS,
            "!=": FilterOperator.NOT_EQUALS,
            ">": FilterOperator.GREATER_THAN,
            "<": FilterOperator.LESS_THAN,
            ">=": FilterOperator.GREATER_THAN_EQUALS,
            "<=": FilterOperator.LESS_THAN_EQUALS,
            "LIKE": FilterOperator.LIKE,
            "IN": FilterOperator.IN,
            "NOT IN": FilterOperator.NOT_IN,
            "IS NULL": FilterOperator.IS_NULL,
            "IS NOT NULL": FilterOperator.IS_NOT_NULL,
        }

        # Handle case-insensitive operator matching
        op_upper = filter_obj.op.upper()
        if op_upper not in operator_mapping:
            raise ValueError(f"Unsupported filter operator: {filter_obj.op}")

        operator = operator_mapping[op_upper]

        # Handle special cases for NULL values
        value = filter_obj.val
        if value is None and operator == FilterOperator.EQUALS:
            operator = FilterOperator.IS_NULL
        elif value is None and operator == FilterOperator.NOT_EQUALS:
            operator = FilterOperator.IS_NOT_NULL

        # Create dataset field configuration
        dataset_field = DatasetFieldConfig(
            dataset=dataset,
            field=FieldConfig(
                field_id=filter_obj.col,
                field_name=filter_obj.col,
            ),
        )

        return cls(
            dataset_field=dataset_field,
            operator=operator,
            value=value,
        )

    def to_sql(self) -> str:
        """Convert the filter to a SQL WHERE clause fragment"""
        if self.operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]:
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()}"
        elif self.operator in [FilterOperator.IN, FilterOperator.NOT_IN]:
            values = ", ".join([format_sql_value(v) for v in self.value])
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} ({values})"
        elif self.operator == FilterOperator.LIKE:
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} {format_sql_value(self.value)}"
        else:
            # Handle string values with quotes
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} {format_sql_value(self.value)}"


class FilterLogic(NameValueStrEnum):
    AND = "AND"
    OR = "OR"

    def to_sql(self) -> str:
        """Convert the filter logic to SQL"""
        return self.value


class FilterGroup(BaseModel):
    """Group of filter conditions with a specific logic operator"""

    filters: list[FilterGroup | FilterConfig]
    logic: FilterLogic = FilterLogic.AND

    def add_filter(self, filter_item: FilterGroup | FilterConfig) -> None:
        """Add a filter to this group"""
        self.filters.append(filter_item)

    def add_filters(self, filter_items: list[FilterGroup | FilterConfig]) -> None:
        """Add multiple filters to this group"""
        self.filters.extend(filter_items)

    def is_empty(self) -> bool:
        """Check if this filter group has no filters"""
        return len(self.filters) == 0

    def to_sql(self) -> str:
        """Convert the filter group to a SQL WHERE clause fragment"""
        if not self.filters:
            return ""

        filter_clauses = []
        for filter_item in self.filters:
            sql_clause = filter_item.to_sql()
            if sql_clause:  # Only add non-empty clauses
                filter_clauses.append(sql_clause)

        if not filter_clauses:
            return ""

        # Join the clauses with the appropriate logic operator
        joined_clauses = f" {self.logic.to_sql()} ".join(filter_clauses)

        # Wrap in parentheses if there's more than one filter
        if len(filter_clauses) > 1:
            return f"({joined_clauses})"
        return joined_clauses


class OrderByConfig(BaseModel):
    """Configuration for an ORDER BY clause"""

    dataset_field: DatasetFieldConfig
    direction: Literal["ASC", "DESC"] = "ASC"

    def to_sql(self) -> str:
        """Convert the order by to a SQL ORDER BY clause fragment"""
        return f"{self.dataset_field.to_sql()} {self.direction}"


class QueryConfig(BaseModel):
    """Complete configuration for a query dataset"""

    columns: list[ColumnConfig] = Field(default_factory=list)
    primary_dataset: DatasetConfig
    additional_datasets: list[JoinConfig] = Field(default_factory=list)
    filter_group: FilterGroup | None = None
    group_bys: list[ColumnConfig] = Field(default_factory=list)
    filter_group: FilterGroup | None = None
    order_bys: list[OrderByConfig] = Field(default_factory=list)
    limit: int | None = None

    @field_validator("limit")
    @classmethod
    def validate_limit(cls, v: int | None) -> int | None:
        if v is not None and v <= 0:
            raise ValueError("limit must be a positive integer")
        return v
