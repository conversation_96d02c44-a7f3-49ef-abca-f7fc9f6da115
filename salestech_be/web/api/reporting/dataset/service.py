from uuid import UUID

from fastapi import Request

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import specified
from salestech_be.db.dao.reporting_dataset_repository import ReportingDatasetRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingDatasetSource,
    ReportingDatasetType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.web.api.reporting.dataset.schema import (
    CreateReportingDatasetRequest,
    PatchReportingDatasetRequest,
    ReportingDatasetDTO,
)

logger = get_logger(__name__)


class ReportingDatasetService:
    def __init__(
        self,
        reporting_dataset_repository: ReportingDatasetRepository,
    ):
        self.reporting_dataset_repository = reporting_dataset_repository

    async def create_dataset(
        self,
        *,
        request: CreateReportingDatasetRequest,
        dataset_source: ReportingDatasetSource = ReportingDatasetSource.INTERNAL,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingDatasetDTO:
        """Create a new dataset with fields."""
        logger.info(
            f"Creating dataset: {request.name} for organization: {organization_id}"
        )

        # Create the dataset and fields using repository
        (
            created_db_dataset,
            created_fields,
        ) = await self.reporting_dataset_repository.create_dataset(
            name=request.name,
            description=request.description,
            query_config=request.query_config,
            dataset_source=dataset_source,
            user_id=user_id,
            organization_id=organization_id,
        )

        return ReportingDatasetDTO.from_db(created_db_dataset, created_fields)

    async def get_dataset(
        self,
        *,
        dataset_id: UUID,
        organization_id: UUID,
    ) -> ReportingDatasetDTO:
        """Get a dataset by ID with its fields."""
        logger.info(
            f"Getting dataset: {dataset_id} for organization: {organization_id}"
        )

        db_dataset = await self.reporting_dataset_repository.get_dataset(
            dataset_id=dataset_id,
            organization_id=organization_id,
        )

        # Get dataset fields
        db_fields = await self.reporting_dataset_repository.list_dataset_fields(
            dataset_id=dataset_id,
            organization_id=organization_id,
        )

        return ReportingDatasetDTO.from_db(db_dataset, db_fields)

    async def patch_dataset(
        self,
        *,
        dataset_id: UUID,
        request: PatchReportingDatasetRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingDatasetDTO:
        """Update a dataset and its fields."""
        logger.info(
            f"Patching dataset: {dataset_id} for organization: {organization_id}"
        )

        # Use repository's patch_dataset method
        (
            updated_db_dataset,
            current_fields,
        ) = await self.reporting_dataset_repository.patch_dataset(
            dataset_id=dataset_id,
            name=request.name,
            description=request.description,
            query_config=request.query_config,
            user_id=user_id,
            organization_id=organization_id,
        )

        return ReportingDatasetDTO.from_db(updated_db_dataset, current_fields)

    async def delete_dataset(
        self,
        *,
        dataset_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        """Delete a dataset and its fields."""
        logger.info(
            f"Deleting dataset: {dataset_id} for organization: {organization_id}"
        )

        # Get existing dataset
        db_dataset = await self.reporting_dataset_repository.get_dataset(
            dataset_id=dataset_id,
            organization_id=organization_id,
        )

        # Only allow deleting QUERY type datasets
        if db_dataset.type != ReportingDatasetType.QUERY:
            raise InvalidArgumentError("Only QUERY type datasets can be deleted")

        # Delete dataset
        await self.reporting_dataset_repository.delete_dataset(
            dataset_id=dataset_id,
            organization_id=organization_id,
            user_id=user_id,
        )

    async def list_datasets(
        self,
        *,
        organization_id: UUID,
    ) -> list[ReportingDatasetDTO]:
        """List all datasets for an organization."""
        logger.info(f"Listing datasets for organization: {organization_id}")

        db_datasets = await self.reporting_dataset_repository.list_datasets(
            organization_id=organization_id,
        )

        # Get fields for all datasets
        result = []
        for db_dataset in db_datasets:
            db_fields = await self.reporting_dataset_repository.list_dataset_fields(
                dataset_id=db_dataset.id,
                organization_id=organization_id,
            )
            result.append(ReportingDatasetDTO.from_db(db_dataset, db_fields))

        return result


def get_reporting_dataset_service(
    request: Request,
) -> ReportingDatasetService:
    db_engine = get_db_engine(request=request)
    return ReportingDatasetService(
        reporting_dataset_repository=ReportingDatasetRepository(engine=db_engine),
    )


def get_reporting_dataset_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> ReportingDatasetService:
    return ReportingDatasetService(
        reporting_dataset_repository=ReportingDatasetRepository(engine=db_engine),
    )
